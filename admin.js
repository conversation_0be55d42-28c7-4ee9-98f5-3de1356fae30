document.addEventListener('DOMContentLoaded', async () => {
    const adminNameSection = document.getElementById('admin-name-section');
    const adminNameInput = document.getElementById('admin-name-input');
    const adminNameSubmitBtn = document.getElementById('admin-name-submit');
    const adminNameStatus = document.getElementById('admin-name-status');

    const adminContentSection = document.getElementById('admin-content-section');
    const adminList = document.getElementById('admin-list');
    const downloadBtn = document.getElementById('download-btn');
    const deleteAllBtn = document.getElementById('delete-all-btn'); // New
    const newOrderBtn = document.getElementById('new-order-btn');     // New
    const loadingStatus = document.getElementById('loading-status');
    const errorStatus = document.getElementById('error-status');
    const captureArea = document.getElementById('capture-area');
    const captureDatetime = document.getElementById('capture-datetime');
    const captureTotal = document.getElementById('capture-total');

    const adminNewOrderSection = document.getElementById('admin-new-order-section'); // New
    const adminOrderForm = document.getElementById('admin-order-form');         // New
    const adminNameField = document.getElementById('admin-name');       // New
    const adminItemField = document.getElementById('admin-item');       // New
    const adminQuantityField = document.getElementById('admin-quantity'); // New
    const adminAmountField = document.getElementById('admin-amount');     // New
    const cancelNewOrderBtn = document.getElementById('cancel-new-order'); // New
    const adminOrderStatus = document.getElementById('admin-order-status'); // New


    const adminName = 'Kamran'; // Hardcoded admin name

    // Initially show the name input section and hide content
    adminNameSection.style.display = 'block';
    adminContentSection.style.display = 'none';
    adminNewOrderSection.style.display = 'none'; // Hide new order form too

    // --- Event Listener for Admin Name Submission ---
    adminNameSubmitBtn.addEventListener('click', () => {
        const enteredName = adminNameInput.value.trim();

        if (enteredName === adminName) {
            // Correct name entered
            adminNameSection.style.display = 'none';
            adminContentSection.style.display = 'block';
            fetchOrders(); // Fetch orders now that access is granted
        } else {
            // Incorrect name
            showAdminNameStatus('Incorrect name.', 'error');
        }
    });

    // Allow pressing Enter in the admin name input field
    adminNameInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
            event.preventDefault();
            adminNameSubmitBtn.click();
        }
    });

    // --- Event Listener for New Order Button ---
    newOrderBtn.addEventListener('click', () => {
        adminContentSection.style.display = 'none';
        adminNewOrderSection.style.display = 'block';
        adminOrderForm.reset(); // Clear the form
        adminOrderStatus.style.display = 'none'; // Hide status
        adminNameField.focus(); // Put focus on the first field
    });

    // --- Event Listener for Cancel New Order Button ---
    cancelNewOrderBtn.addEventListener('click', () => {
        adminNewOrderSection.style.display = 'none';
        adminContentSection.style.display = 'block';
        fetchOrders(); // Refresh list when returning
    });

    // --- Event Listener for Admin Order Form Submission ---
    adminOrderForm.addEventListener('submit', async (event) => {
        event.preventDefault();

        const name = adminNameField.value.trim();
        const item = adminItemField.value.trim();
        const quantity = adminQuantityField.value.trim();
        const amount = adminAmountField.value.trim();

         if (!name || !item || !quantity || !amount) {
            showAdminOrderStatus('Please fill in all fields.', 'error');
            return;
        }

        const orderData = {
            name: name,
            item: item,
            quantity: quantity,
            amount: amount
        };

        try {
            // Send POST request to the same /submit endpoint
            // Add admin name for backend check (simple validation)
            const response = await fetch(`/submit?name=${adminName}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData),
            });

            const result = await response.json();

            if (result.success) {
                showAdminOrderStatus('Order submitted successfully!', 'success');
                // After successful submission, hide the form and show the list
                // Use a slight delay to allow user to see the success message
                setTimeout(() => {
                    adminNewOrderSection.style.display = 'none';
                    adminContentSection.style.display = 'block';
                    fetchOrders(); // Refresh the list
                }, 1000); // 1 second delay
            } else {
                showAdminOrderStatus(`Submission failed: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Error submitting admin order:', error);
            showAdminOrderStatus('An error occurred while submitting.', 'error');
        }
    });


    // --- Fetch Orders Function ---
    async function fetchOrders() {
        loadingStatus.style.display = 'block';
        errorStatus.style.display = 'none';
        adminList.innerHTML = ''; // Clear previous list

        try {
            // Include the admin name in the request for backend validation
            const response = await fetch(`/admin/data?name=${adminName}`);
            const result = await response.json();

            loadingStatus.style.display = 'none';

            if (result.success) {
                displayOrders(result.orders);
            } else {
                errorStatus.textContent = `Error: ${result.message}`;
                errorStatus.style.display = 'block';
            }
        } catch (error) {
            console.error('Error fetching orders:', error);
            loadingStatus.style.display = 'none';
            errorStatus.textContent = 'An error occurred while fetching orders.';
            errorStatus.style.display = 'block';
        }
    }

    // --- Display Orders Function ---
    function displayOrders(orders) {
        let totalAmount = 0;
        adminList.innerHTML = ''; // Clear list

        if (orders.length === 0) {
            adminList.innerHTML = '<li class="no-orders">No orders submitted yet.</li>';
            captureTotal.textContent = 'Total Amount: 0';
             // Hide elements if no orders
            captureArea.querySelector('.capture-footer').style.display = 'none';
            return;
        }

        captureArea.querySelector('.capture-footer').style.display = 'block'; // Show footer if orders exist

        // Group orders by user and timestamp (to group multi-item orders)
        const groupedOrders = groupOrdersByUser(orders);

        groupedOrders.forEach(userGroup => {
            const orderCard = document.createElement('li');
            orderCard.className = 'order-card';

            // Create items list for this user's order
            let itemsHtml = '';
            let orderIds = [];
            let totalUserAmount = 0;
            let timestamps = [];

            userGroup.orders.forEach((order) => {
                orderIds.push(order.id);
                timestamps.push(new Date(order.timestamp));

                itemsHtml += `
                    <div class="order-item">
                        <span class="item-name">${order.item}</span>
                        <span class="item-quantity">Qty: ${order.quantity}</span>
                        <span class="item-time">${new Date(order.timestamp).toLocaleString()}</span>
                    </div>
                `;

                // Calculate total amount for this user
                const amountValue = parseFloat(order.amount.replace(/[^0-9.-]+/g,""));
                if (!isNaN(amountValue)) {
                    totalUserAmount += amountValue;
                }
            });

            // Create time range display
            const earliestTime = new Date(Math.min(...timestamps));
            const latestTime = new Date(Math.max(...timestamps));
            let timeDisplay = '';

            if (timestamps.length === 1) {
                timeDisplay = latestTime.toLocaleString();
            } else {
                timeDisplay = `${earliestTime.toLocaleString()} - ${latestTime.toLocaleString()}`;
            }

            orderCard.innerHTML = `
                <div class="order-header">
                    <div class="order-user">
                        <strong>${userGroup.name}</strong>
                        <span class="order-time">${timeDisplay}</span>
                        <span class="order-count">${userGroup.orders.length} item(s)</span>
                    </div>
                    <div class="order-actions">
                        <button class="delete-order-btn" data-ids="${orderIds.join(',')}" title="Delete all orders for ${userGroup.name}">
                            <span class="delete-icon">×</span>
                        </button>
                    </div>
                </div>
                <div class="order-items">
                    ${itemsHtml}
                </div>
                <div class="order-footer">
                    <span class="order-amount">Total: Rs ${totalUserAmount.toFixed(2)}</span>
                </div>
            `;

            adminList.appendChild(orderCard);

            // Add to grand total
            totalAmount += totalUserAmount;
        });

        // Display total amount
        captureTotal.textContent = `Total Amount: ${totalAmount.toFixed(2)}`;
    }

    // --- Group Orders by User Function ---
    function groupOrdersByUser(orders) {
        const groups = {};

        orders.forEach(order => {
            // Group by name only (all orders from same user together)
            const groupKey = order.name;

            if (!groups[groupKey]) {
                groups[groupKey] = {
                    name: order.name,
                    orders: []
                };
            }

            groups[groupKey].orders.push(order);
        });

        // Sort orders within each group by timestamp (most recent first)
        Object.values(groups).forEach(group => {
            group.orders.sort((a, b) => {
                const timeA = new Date(a.timestamp).getTime();
                const timeB = new Date(b.timestamp).getTime();
                return timeB - timeA;
            });
        });

        // Convert to array and sort groups by most recent order in each group
        return Object.values(groups).sort((a, b) => {
            const timeA = new Date(a.orders[0].timestamp).getTime();
            const timeB = new Date(b.orders[0].timestamp).getTime();
            return timeB - timeA;
        });
    }

    // --- Event Delegation for Delete Buttons ---
    adminList.addEventListener('click', (event) => {
        // Check if the clicked element is a delete button or its child
        const deleteBtn = event.target.closest('.delete-order-btn');
        if (deleteBtn) {
            const orderIds = deleteBtn.dataset.ids.split(',');
            const userName = deleteBtn.closest('.order-card').querySelector('.order-user strong').textContent;

            if (confirm(`Are you sure you want to delete ${userName}'s order?`)) {
                deleteOrders(orderIds);
            }
        }
    });

    // --- Function to Delete Multiple Orders ---
    async function deleteOrders(orderIds) {
        try {
            // Delete each order individually
            const deletePromises = orderIds.map(orderId =>
                fetch(`/delete/${orderId}?name=${adminName}`, {
                    method: 'DELETE',
                })
            );

            const responses = await Promise.all(deletePromises);
            const results = await Promise.all(responses.map(r => r.json()));

            // Check if all deletions were successful
            const allSuccessful = results.every(result => result.success);

            if (allSuccessful) {
                console.log('All orders deleted successfully');
                fetchOrders(); // Refresh the list after deletion
            } else {
                console.error('Some deletions failed:', results);
                alert('Some orders could not be deleted. Please try again.');
            }
        } catch (error) {
            console.error('Error deleting orders:', error);
            alert('An error occurred while deleting the orders.');
        }
    }

    // --- Event Listener for Delete All Button ---
    deleteAllBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to delete ALL orders? This cannot be undone.')) {
            deleteAllOrders();
        }
    });

     // --- Function to Delete All Orders ---
    async function deleteAllOrders() {
        try {
            // Add admin name for backend check (simple validation)
            const response = await fetch(`/delete_all?name=${adminName}`, {
                method: 'DELETE',
            });

            const result = await response.json();

            if (result.success) {
                console.log(result.message);
                fetchOrders(); // Refresh the list after deletion
            } else {
                console.error(`Delete All failed: ${result.message}`);
                alert(`Failed to delete all orders: ${result.message}`);
            }
        } catch (error) {
            console.error('Error deleting all orders:', error);
            alert('An error occurred while deleting all orders.');
        }
    }


    // --- Download Button Handler - Generate PDF ---
    downloadBtn.addEventListener('click', () => {
        generatePDF();
    });

    // --- Generate PDF Function ---
    function generatePDF() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        const now = new Date();
        const timestamp = now.toISOString().replace(/[:.-]/g, '');

        // PDF Configuration
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const margin = 20;
        const lineHeight = 6;
        let yPosition = margin;

        // Header
        doc.setFontSize(18);
        doc.setFont(undefined, 'bold');
        doc.text('Office Peon Orders', pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 10;

        doc.setFontSize(12);
        doc.setFont(undefined, 'normal');
        doc.text(`Generated: ${now.toLocaleString()}`, pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 15;

        // Get grouped orders data
        const orders = Array.from(adminList.querySelectorAll('.order-card'));

        if (orders.length === 0) {
            doc.setFontSize(14);
            doc.text('No orders found.', pageWidth / 2, yPosition, { align: 'center' });
        } else {
            // Process each order group
            orders.forEach((orderCard, index) => {
                // Check if we need a new page
                if (yPosition > pageHeight - 40) {
                    doc.addPage();
                    yPosition = margin;
                }

                // User name
                const userName = orderCard.querySelector('.order-user strong').textContent;
                const orderTime = orderCard.querySelector('.order-time').textContent;
                const orderCount = orderCard.querySelector('.order-count').textContent;
                const orderAmount = orderCard.querySelector('.order-amount').textContent;

                doc.setFontSize(14);
                doc.setFont(undefined, 'bold');
                doc.text(`${userName}`, margin, yPosition);

                doc.setFontSize(10);
                doc.setFont(undefined, 'normal');
                doc.text(`${orderTime} | ${orderCount}`, margin, yPosition + 5);
                yPosition += 12;

                // Items
                const items = orderCard.querySelectorAll('.order-item');
                items.forEach(item => {
                    const itemName = item.querySelector('.item-name').textContent;
                    const itemQuantity = item.querySelector('.item-quantity').textContent;
                    const itemTime = item.querySelector('.item-time').textContent;

                    doc.setFontSize(11);
                    doc.text(`• ${itemName}`, margin + 5, yPosition);
                    doc.setFontSize(9);
                    doc.text(`${itemQuantity}`, margin + 80, yPosition);
                    doc.text(`${itemTime}`, margin + 120, yPosition);
                    yPosition += lineHeight;
                });

                // Amount
                doc.setFontSize(12);
                doc.setFont(undefined, 'bold');
                doc.text(orderAmount, pageWidth - margin, yPosition, { align: 'right' });
                yPosition += 10;

                // Separator line
                if (index < orders.length - 1) {
                    doc.setDrawColor(200, 200, 200);
                    doc.line(margin, yPosition, pageWidth - margin, yPosition);
                    yPosition += 8;
                }
            });

            // Total at the bottom
            yPosition += 5;
            const totalText = captureTotal.textContent;
            doc.setFontSize(14);
            doc.setFont(undefined, 'bold');
            doc.text(totalText, pageWidth - margin, yPosition, { align: 'right' });
        }

        // Footer
        const footerY = pageHeight - 15;
        doc.setFontSize(8);
        doc.setFont(undefined, 'normal');
        doc.setTextColor(128, 128, 128);
        doc.text('Generated by Office Ordering System', pageWidth / 2, footerY, { align: 'center' });

        // Save the PDF
        doc.save(`office_orders_${timestamp}.pdf`);
    }

    // --- Helper function for admin name input status ---
    function showAdminNameStatus(message, type) {
        adminNameStatus.textContent = message;
        adminNameStatus.className = `status ${type}`; // 'status success' or 'status error'
        adminNameStatus.style.display = 'block';
    }

    // --- Helper function for admin order status ---
     function showAdminOrderStatus(message, type) {
        adminOrderStatus.textContent = message;
        adminOrderStatus.className = `status ${type}`; // 'status success' or 'status error'
        adminOrderStatus.style.display = 'block';
    }

    // No initial fetchOrders call here, it happens after successful name entry
});
