document.addEventListener('DOMContentLoaded', async () => {
    const adminNameSection = document.getElementById('admin-name-section');
    const adminNameInput = document.getElementById('admin-name-input');
    const adminNameSubmitBtn = document.getElementById('admin-name-submit');
    const adminNameStatus = document.getElementById('admin-name-status');

    const adminContentSection = document.getElementById('admin-content-section');
    const adminList = document.getElementById('admin-list');
    const downloadBtn = document.getElementById('download-btn');
    const deleteAllBtn = document.getElementById('delete-all-btn'); // New
    const newOrderBtn = document.getElementById('new-order-btn');     // New
    const loadingStatus = document.getElementById('loading-status');
    const errorStatus = document.getElementById('error-status');
    const captureArea = document.getElementById('capture-area');
    const captureTotal = document.getElementById('capture-total');

    const adminNewOrderSection = document.getElementById('admin-new-order-section'); // New
    const adminOrderForm = document.getElementById('admin-order-form');         // New
    const adminNameField = document.getElementById('admin-name');       // New
    const adminItemField = document.getElementById('admin-item');       // New
    const adminQuantityField = document.getElementById('admin-quantity'); // New
    const adminAmountField = document.getElementById('admin-amount');     // New
    const cancelNewOrderBtn = document.getElementById('cancel-new-order'); // New
    const adminOrderStatus = document.getElementById('admin-order-status'); // New


    const adminName = 'Kamran'; // Hardcoded admin name

    // Initially show the name input section and hide content
    adminNameSection.style.display = 'block';
    adminContentSection.style.display = 'none';
    adminNewOrderSection.style.display = 'none'; // Hide new order form too

    // --- Event Listener for Admin Name Submission ---
    adminNameSubmitBtn.addEventListener('click', () => {
        const enteredName = adminNameInput.value.trim();

        if (enteredName === adminName) {
            // Correct name entered
            adminNameSection.style.display = 'none';
            adminContentSection.style.display = 'block';
            fetchOrders(); // Fetch orders now that access is granted
        } else {
            // Incorrect name
            showAdminNameStatus('Incorrect name.', 'error');
        }
    });

    // Allow pressing Enter in the admin name input field
    adminNameInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
            event.preventDefault();
            adminNameSubmitBtn.click();
        }
    });

    // --- Event Listener for New Order Button ---
    newOrderBtn.addEventListener('click', () => {
        adminContentSection.style.display = 'none';
        adminNewOrderSection.style.display = 'block';
        adminOrderForm.reset(); // Clear the form
        adminOrderStatus.style.display = 'none'; // Hide status
        adminNameField.focus(); // Put focus on the first field
    });

    // --- Event Listener for Cancel New Order Button ---
    cancelNewOrderBtn.addEventListener('click', () => {
        adminNewOrderSection.style.display = 'none';
        adminContentSection.style.display = 'block';
        fetchOrders(); // Refresh list when returning
    });

    // --- Event Listener for Admin Order Form Submission ---
    adminOrderForm.addEventListener('submit', async (event) => {
        event.preventDefault();

        const name = adminNameField.value.trim();
        const item = adminItemField.value.trim();
        const quantity = adminQuantityField.value.trim();
        const amount = adminAmountField.value.trim();

         if (!name || !item || !quantity || !amount) {
            showAdminOrderStatus('Please fill in all fields.', 'error');
            return;
        }

        const orderData = {
            name: name,
            item: item,
            quantity: quantity,
            amount: amount
        };

        try {
            // Send POST request to the same /submit endpoint
            // Add admin name for backend check (simple validation)
            const response = await fetch(`/submit?name=${adminName}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData),
            });

            const result = await response.json();

            if (result.success) {
                showAdminOrderStatus('Order submitted successfully!', 'success');
                // After successful submission, hide the form and show the list
                // Use a slight delay to allow user to see the success message
                setTimeout(() => {
                    adminNewOrderSection.style.display = 'none';
                    adminContentSection.style.display = 'block';
                    fetchOrders(); // Refresh the list
                }, 1000); // 1 second delay
            } else {
                showAdminOrderStatus(`Submission failed: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Error submitting admin order:', error);
            showAdminOrderStatus('An error occurred while submitting.', 'error');
        }
    });


    // --- Fetch Orders Function ---
    async function fetchOrders() {
        loadingStatus.style.display = 'block';
        errorStatus.style.display = 'none';
        adminList.innerHTML = ''; // Clear previous list

        try {
            // Include the admin name in the request for backend validation
            const response = await fetch(`/admin/data?name=${adminName}`);
            const result = await response.json();

            loadingStatus.style.display = 'none';

            if (result.success) {
                displayOrders(result.orders);
            } else {
                errorStatus.textContent = `Error: ${result.message}`;
                errorStatus.style.display = 'block';
            }
        } catch (error) {
            console.error('Error fetching orders:', error);
            loadingStatus.style.display = 'none';
            errorStatus.textContent = 'An error occurred while fetching orders.';
            errorStatus.style.display = 'block';
        }
    }

    // --- Display Orders Function ---
    function displayOrders(orders) {
        let totalAmount = 0;
        adminList.innerHTML = ''; // Clear list

        if (orders.length === 0) {
            adminList.innerHTML = '<li class="no-orders">No orders submitted yet.</li>';
            captureTotal.textContent = 'Total Amount: 0';
             // Hide elements if no orders
            captureArea.querySelector('.capture-footer').style.display = 'none';
            return;
        }

        captureArea.querySelector('.capture-footer').style.display = 'block'; // Show footer if orders exist

        // Group orders by user and timestamp (to group multi-item orders)
        const groupedOrders = groupOrdersByUser(orders);

        groupedOrders.forEach(userGroup => {
            const orderCard = document.createElement('li');
            orderCard.className = 'order-card';

            // Create items list for this user's order
            let itemsHtml = '';
            let orderIds = [];
            let totalUserAmount = 0;
            let timestamps = [];

            userGroup.orders.forEach((order) => {
                orderIds.push(order.id);
                timestamps.push(new Date(order.timestamp));

                itemsHtml += `
                    <div class="order-item">
                        <span class="item-name">${order.item}</span>
                        <span class="item-quantity">Qty: ${order.quantity}</span>
                        <span class="item-time">${new Date(order.timestamp).toLocaleString()}</span>
                    </div>
                `;

                // Calculate total amount for this user
                const amountValue = parseFloat(order.amount.replace(/[^0-9.-]+/g,""));
                if (!isNaN(amountValue)) {
                    totalUserAmount += amountValue;
                }
            });

            // Create time range display
            const earliestTime = new Date(Math.min(...timestamps));
            const latestTime = new Date(Math.max(...timestamps));
            let timeDisplay = '';

            if (timestamps.length === 1) {
                timeDisplay = latestTime.toLocaleString();
            } else {
                timeDisplay = `${earliestTime.toLocaleString()} - ${latestTime.toLocaleString()}`;
            }

            orderCard.innerHTML = `
                <div class="order-header">
                    <div class="order-user">
                        <strong>${userGroup.name}</strong>
                        <span class="order-time">${timeDisplay}</span>
                        <span class="order-count">${userGroup.orders.length} item(s)</span>
                    </div>
                    <div class="order-actions">
                        <button class="delete-order-btn" data-ids="${orderIds.join(',')}" title="Delete all orders for ${userGroup.name}">
                            <span class="delete-icon">×</span>
                        </button>
                    </div>
                </div>
                <div class="order-items">
                    ${itemsHtml}
                </div>
                <div class="order-footer">
                    <span class="order-amount">Total: Rs ${totalUserAmount.toFixed(2)}</span>
                </div>
            `;

            adminList.appendChild(orderCard);

            // Add to grand total
            totalAmount += totalUserAmount;
        });

        // Display total amount
        captureTotal.textContent = `Total Amount: ${totalAmount.toFixed(2)}`;
    }

    // --- Group Orders by User Function ---
    function groupOrdersByUser(orders) {
        const groups = {};

        orders.forEach(order => {
            // Group by name only (all orders from same user together)
            const groupKey = order.name;

            if (!groups[groupKey]) {
                groups[groupKey] = {
                    name: order.name,
                    orders: []
                };
            }

            groups[groupKey].orders.push(order);
        });

        // Sort orders within each group by timestamp (most recent first)
        Object.values(groups).forEach(group => {
            group.orders.sort((a, b) => {
                const timeA = new Date(a.timestamp).getTime();
                const timeB = new Date(b.timestamp).getTime();
                return timeB - timeA;
            });
        });

        // Convert to array and sort groups by most recent order in each group
        return Object.values(groups).sort((a, b) => {
            const timeA = new Date(a.orders[0].timestamp).getTime();
            const timeB = new Date(b.orders[0].timestamp).getTime();
            return timeB - timeA;
        });
    }

    // --- Event Delegation for Delete Buttons ---
    adminList.addEventListener('click', (event) => {
        // Check if the clicked element is a delete button or its child
        const deleteBtn = event.target.closest('.delete-order-btn');
        if (deleteBtn) {
            const orderIds = deleteBtn.dataset.ids.split(',');
            const userName = deleteBtn.closest('.order-card').querySelector('.order-user strong').textContent;

            if (confirm(`Are you sure you want to delete ${userName}'s order?`)) {
                deleteOrders(orderIds);
            }
        }
    });

    // --- Function to Delete Multiple Orders ---
    async function deleteOrders(orderIds) {
        try {
            // Delete each order individually
            const deletePromises = orderIds.map(orderId =>
                fetch(`/delete/${orderId}?name=${adminName}`, {
                    method: 'DELETE',
                })
            );

            const responses = await Promise.all(deletePromises);
            const results = await Promise.all(responses.map(r => r.json()));

            // Check if all deletions were successful
            const allSuccessful = results.every(result => result.success);

            if (allSuccessful) {
                console.log('All orders deleted successfully');
                fetchOrders(); // Refresh the list after deletion
            } else {
                console.error('Some deletions failed:', results);
                alert('Some orders could not be deleted. Please try again.');
            }
        } catch (error) {
            console.error('Error deleting orders:', error);
            alert('An error occurred while deleting the orders.');
        }
    }

    // --- Event Listener for Delete All Button ---
    deleteAllBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to delete ALL orders? This cannot be undone.')) {
            deleteAllOrders();
        }
    });

     // --- Function to Delete All Orders ---
    async function deleteAllOrders() {
        try {
            // Add admin name for backend check (simple validation)
            const response = await fetch(`/delete_all?name=${adminName}`, {
                method: 'DELETE',
            });

            const result = await response.json();

            if (result.success) {
                console.log(result.message);
                fetchOrders(); // Refresh the list after deletion
            } else {
                console.error(`Delete All failed: ${result.message}`);
                alert(`Failed to delete all orders: ${result.message}`);
            }
        } catch (error) {
            console.error('Error deleting all orders:', error);
            alert('An error occurred while deleting all orders.');
        }
    }


    // --- Download Button Handler - Generate PDF ---
    downloadBtn.addEventListener('click', () => {
        generatePDF();
    });

    // --- Roman Urdu to Urdu Translation Dictionary ---
    const urduTranslations = {
        // Food items
        'daal chawal': 'دال چاول',
        'daal': 'دال',
        'chawal': 'چاول',
        'coffee': 'کافی',
        'tea': 'چائے',
        'chai': 'چائے',
        'pani': 'پانی',
        'water': 'پانی',
        'roti': 'روٹی',
        'naan': 'نان',
        'biryani': 'بریانی',
        'karahi': 'کڑاہی',
        'qorma': 'قورمہ',
        'kebab': 'کباب',
        'samosa': 'سموسہ',
        'pakora': 'پکوڑا',
        'snacks': 'ناشتہ',
        'biscuit': 'بسکٹ',
        'chips': 'چپس',
        'juice': 'جوس',
        'milk': 'دودھ',
        'lassi': 'لسی',
        'paratha': 'پراٹھا',
        'halwa': 'حلوہ',
        'kheer': 'کھیر',

        // Common words
        'hello world': 'ہیلو ورلڈ',
        'printout': 'پرنٹ آؤٹ',
        'photocopy': 'فوٹو کاپی',
        'xerox': 'زیراکس',

        // Quantities
        'cup': 'کپ',
        'glass': 'گلاس',
        'plate': 'پلیٹ',
        'bowl': 'پیالہ',
        'packet': 'پیکٹ',
        'bottle': 'بوتل',
        'piece': 'ٹکڑا',
        'pages': 'صفحات'
    };

    // --- Function to translate Roman Urdu to Urdu ---
    function translateToUrdu(text) {
        if (!text) return text;

        let translatedText = text.toLowerCase();

        // Replace known translations
        for (const [roman, urdu] of Object.entries(urduTranslations)) {
            const regex = new RegExp(roman, 'gi');
            translatedText = translatedText.replace(regex, urdu);
        }

        // If no translation found, return original text
        return translatedText === text.toLowerCase() ? text : translatedText;
    }

    // --- Generate PDF Function with Urdu Support ---
    function generatePDF() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        const now = new Date();
        const timestamp = now.toISOString().replace(/[:.-]/g, '');

        // PDF Configuration
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const margin = 15;
        let yPosition = margin;

        // Header in Urdu
        doc.setFontSize(20);
        doc.setFont(undefined, 'bold');
        doc.text('دفتری آرڈرز کی فہرست', pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 8;

        doc.setFontSize(12);
        doc.setFont(undefined, 'normal');
        doc.text(`تاریخ: ${now.toLocaleDateString('ur-PK')} وقت: ${now.toLocaleTimeString('ur-PK')}`, pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 15;

        // Table headers
        const tableStartY = yPosition;
        const colWidths = {
            sno: 15,      // S.No
            naam: 45,     // Name
            items: 80,    // Items
            qeemat: 35    // Amount
        };

        const colPositions = {
            sno: margin,
            naam: margin + colWidths.sno,
            items: margin + colWidths.sno + colWidths.naam,
            qeemat: margin + colWidths.sno + colWidths.naam + colWidths.items
        };

        // Draw table header
        doc.setFillColor(240, 240, 240);
        doc.rect(margin, yPosition, pageWidth - 2 * margin, 12, 'F');

        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text('نمبر', colPositions.sno + 5, yPosition + 8);
        doc.text('نام', colPositions.naam + 5, yPosition + 8);
        doc.text('کیا لانا ہے', colPositions.items + 5, yPosition + 8);
        doc.text('قیمت', colPositions.qeemat + 5, yPosition + 8);

        // Draw header border
        doc.setDrawColor(0, 0, 0);
        doc.rect(margin, yPosition, pageWidth - 2 * margin, 12);
        yPosition += 12;

        // Get grouped orders data
        const orders = Array.from(adminList.querySelectorAll('.order-card'));
        let rowNumber = 1;
        let grandTotal = 0;

        if (orders.length === 0) {
            doc.setFontSize(14);
            doc.text('کوئی آرڈر نہیں ملا', pageWidth / 2, yPosition + 20, { align: 'center' });
        } else {
            doc.setFont(undefined, 'normal');
            doc.setFontSize(10);

            // Process each order group
            orders.forEach((orderCard) => {
                // Check if we need a new page
                if (yPosition > pageHeight - 50) {
                    doc.addPage();
                    yPosition = margin;

                    // Redraw header on new page
                    doc.setFillColor(240, 240, 240);
                    doc.rect(margin, yPosition, pageWidth - 2 * margin, 12, 'F');
                    doc.setFontSize(12);
                    doc.setFont(undefined, 'bold');
                    doc.text('نمبر', colPositions.sno + 5, yPosition + 8);
                    doc.text('نام', colPositions.naam + 5, yPosition + 8);
                    doc.text('کیا لانا ہے', colPositions.items + 5, yPosition + 8);
                    doc.text('قیمت', colPositions.qeemat + 5, yPosition + 8);
                    doc.rect(margin, yPosition, pageWidth - 2 * margin, 12);
                    yPosition += 12;
                    doc.setFont(undefined, 'normal');
                    doc.setFontSize(10);
                }

                // Get order data
                const userName = orderCard.querySelector('.order-user strong').textContent;
                const orderAmount = orderCard.querySelector('.order-amount').textContent;
                const items = orderCard.querySelectorAll('.order-item');

                // Extract amount value
                const amountMatch = orderAmount.match(/[\d.]+/);
                const amountValue = amountMatch ? parseFloat(amountMatch[0]) : 0;
                grandTotal += amountValue;

                // Prepare items text
                let itemsText = '';
                items.forEach((item, index) => {
                    const itemName = item.querySelector('.item-name').textContent;
                    const itemQuantity = item.querySelector('.item-quantity').textContent.replace('Qty: ', '');

                    const translatedItem = translateToUrdu(itemName);
                    const translatedQuantity = translateToUrdu(itemQuantity);

                    if (index > 0) itemsText += '\n';
                    itemsText += `${translatedItem} (${translatedQuantity})`;
                });

                // Calculate row height based on items
                const itemLines = itemsText.split('\n');
                const rowHeight = Math.max(12, itemLines.length * 6 + 6);

                // Draw row background (alternating colors)
                if (rowNumber % 2 === 0) {
                    doc.setFillColor(250, 250, 250);
                    doc.rect(margin, yPosition, pageWidth - 2 * margin, rowHeight, 'F');
                }

                // Draw row content
                doc.text(rowNumber.toString(), colPositions.sno + 5, yPosition + 8);
                doc.text(userName, colPositions.naam + 5, yPosition + 8);

                // Draw items (multi-line support)
                const itemsLines = doc.splitTextToSize(itemsText, colWidths.items - 10);
                doc.text(itemsLines, colPositions.items + 5, yPosition + 8);

                doc.text(`Rs ${amountValue.toFixed(0)}`, colPositions.qeemat + 5, yPosition + 8);

                // Draw row border
                doc.setDrawColor(200, 200, 200);
                doc.rect(margin, yPosition, pageWidth - 2 * margin, rowHeight);

                yPosition += rowHeight;
                rowNumber++;
            });

            // Draw total row
            yPosition += 5;
            doc.setFillColor(220, 220, 220);
            doc.rect(margin, yPosition, pageWidth - 2 * margin, 12, 'F');

            doc.setFontSize(12);
            doc.setFont(undefined, 'bold');
            doc.text('کل رقم:', colPositions.items + 5, yPosition + 8);
            doc.text(`Rs ${grandTotal.toFixed(0)}`, colPositions.qeemat + 5, yPosition + 8);

            doc.setDrawColor(0, 0, 0);
            doc.rect(margin, yPosition, pageWidth - 2 * margin, 12);
        }

        // Footer
        const footerY = pageHeight - 15;
        doc.setFontSize(8);
        doc.setFont(undefined, 'normal');
        doc.setTextColor(128, 128, 128);
        doc.text('دفتری آرڈرنگ سسٹم کے ذریعے تیار کیا گیا', pageWidth / 2, footerY, { align: 'center' });

        // Save the PDF
        doc.save(`kamran_orders_${timestamp}.pdf`);
    }

    // --- Helper function for admin name input status ---
    function showAdminNameStatus(message, type) {
        adminNameStatus.textContent = message;
        adminNameStatus.className = `status ${type}`; // 'status success' or 'status error'
        adminNameStatus.style.display = 'block';
    }

    // --- Helper function for admin order status ---
     function showAdminOrderStatus(message, type) {
        adminOrderStatus.textContent = message;
        adminOrderStatus.className = `status ${type}`; // 'status success' or 'status error'
        adminOrderStatus.style.display = 'block';
    }

    // No initial fetchOrders call here, it happens after successful name entry
});
