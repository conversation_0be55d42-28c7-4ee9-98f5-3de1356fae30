import http.server
import socketserver
import sqlite3
import json
import os
from urllib.parse import urlparse, parse_qs
import re # Import regex for path parsing

PORT = 8000 # Changed port back to 8000
DATABASE_FILE = 'orders.db'
ADMIN_NAME = 'Kamran' # Define admin name here

# --- Database Setup ---
def setup_database():
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            item TEXT NOT NULL,
            quantity TEXT NOT NULL,
            amount TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    conn.commit()
    conn.close()

# --- Custom Request Handler ---
class PeonAppHandler(http.server.SimpleHTTPRequestHandler):

    # Helper to check admin access based on query parameter
    def is_admin(self):
        parsed_url = urlparse(self.path)
        query = parse_qs(parsed_url.query)
        return query.get('name', [''])[0] == ADMIN_NAME

    def do_POST(self):
        # Reuse /submit for both user and admin forms
        if self.path.startswith('/submit'): # Use startswith to handle potential query params
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data)
                name = data.get('name')
                item = data.get('item')
                quantity = data.get('quantity')
                amount = data.get('amount')

                if not all([name, item, quantity, amount]):
                    self.send_response(400)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({'success': False, 'message': 'Missing fields'}).encode('utf-8'))
                    return

                conn = sqlite3.connect(DATABASE_FILE)
                cursor = conn.cursor()
                cursor.execute("INSERT INTO orders (name, item, quantity, amount) VALUES (?, ?, ?, ?)",
                               (name, item, quantity, amount))
                conn.commit()
                conn.close()

                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'success': True, 'message': 'Order submitted'}).encode('utf-8'))

            except json.JSONDecodeError:
                self.send_response(400)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'success': False, 'message': 'Invalid JSON'}).encode('utf-8'))
            except Exception as e:
                print(f"Database error on POST: {e}")
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'success': False, 'message': 'Server error'}).encode('utf-8'))

        else:
            self.send_response(404)
            self.end_headers()

    def do_GET(self):
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query = parse_qs(parsed_url.query)

        if path == '/admin/data':
            # Basic admin check
            if not self.is_admin():
                 self.send_response(403)
                 self.send_header('Content-type', 'application/json')
                 self.end_headers()
                 self.wfile.write(json.dumps({'success': False, 'message': 'Unauthorized'}).encode('utf-8'))
                 return

            try:
                conn = sqlite3.connect(DATABASE_FILE)
                cursor = conn.cursor()
                # Select the id column as well
                cursor.execute("SELECT id, name, item, quantity, amount, timestamp FROM orders ORDER BY timestamp DESC")
                rows = cursor.fetchall()
                conn.close()

                # Convert rows to a list of dictionaries for JSON response
                orders = []
                for row in rows:
                    orders.append({
                        'id': row[0], # Include id
                        'name': row[1],
                        'item': row[2],
                        'quantity': row[3],
                        'amount': row[4],
                        'timestamp': row[5]
                    })

                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'success': True, 'orders': orders}).encode('utf-8'))

            except Exception as e:
                print(f"Database error on GET /admin/data: {e}")
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'success': False, 'message': 'Server error'}).encode('utf-8'))

        elif path == '/admin' or path == '/admin/':
             # Serve admin.html
             self.path = '/admin.html'
             return http.server.SimpleHTTPRequestHandler.do_GET(self)

        else:
            # Serve other static files (index.html, css, js)
            return http.server.SimpleHTTPRequestHandler.do_GET(self)

    def do_DELETE(self):
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # Basic admin check for delete operations
        if not self.is_admin():
             self.send_response(403)
             self.send_header('Content-type', 'application/json')
             self.end_headers()
             self.wfile.write(json.dumps({'success': False, 'message': 'Unauthorized'}).encode('utf-8'))
             return

        # Handle delete all
        if path == '/delete_all':
            try:
                conn = sqlite3.connect(DATABASE_FILE)
                cursor = conn.cursor()
                cursor.execute("DELETE FROM orders")
                conn.commit()
                conn.close()

                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'success': True, 'message': 'All orders deleted'}).encode('utf-8'))

            except Exception as e:
                print(f"Database error on DELETE /delete_all: {e}")
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'success': False, 'message': 'Server error'}).encode('utf-8'))

        # Handle delete single order
        elif path.startswith('/delete/'):
            # Use regex to extract ID from path like /delete/123
            match = re.match(r'/delete/(\d+)', path)
            if match:
                order_id = match.group(1)
                try:
                    conn = sqlite3.connect(DATABASE_FILE)
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM orders WHERE id = ?", (order_id,))
                    conn.commit()
                    # Check if any row was actually deleted
                    if cursor.rowcount > 0:
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({'success': True, 'message': f'Order {order_id} deleted'}).encode('utf-8'))
                    else:
                         self.send_response(404) # Not Found if ID didn't exist
                         self.send_header('Content-type', 'application/json')
                         self.end_headers()
                         self.wfile.write(json.dumps({'success': False, 'message': f'Order {order_id} not found'}).encode('utf-8'))
                    conn.close()

                except Exception as e:
                    print(f"Database error on DELETE /delete/{order_id}: {e}")
                    self.send_response(500)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({'success': False, 'message': 'Server error'}).encode('utf-8'))
            else:
                self.send_response(400) # Bad Request if ID is missing or invalid format
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'success': False, 'message': 'Invalid delete path'}).encode('utf-8'))

        else:
            self.send_response(404)
            self.end_headers()


# --- Main Server Execution ---
if __name__ == "__main__":
    setup_database()
    # Set the current directory to the script's directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    Handler = PeonAppHandler
    # Running on a non-privileged port like 8000 does NOT require root privileges
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"Serving at port {PORT}")
        print(f"Access the app directly at http://************:{PORT}/")
        print(f"Access the admin page directly at http://************:{PORT}/admin")
        print(f"\n*** To access via http://kamran.com/, you MUST configure your OpenWrt switch DNS and Firewall Redirect. ***")
        httpd.serve_forever()
