document.addEventListener('DOMContentLoaded', async () => {
    const adminNameSection = document.getElementById('admin-name-section');
    const adminNameInput = document.getElementById('admin-name-input');
    const adminNameSubmitBtn = document.getElementById('admin-name-submit');
    const adminNameStatus = document.getElementById('admin-name-status');

    const adminContentSection = document.getElementById('admin-content-section');
    const adminList = document.getElementById('admin-list');
    const downloadBtn = document.getElementById('download-btn');
    const deleteAllBtn = document.getElementById('delete-all-btn'); // New
    const newOrderBtn = document.getElementById('new-order-btn');     // New
    const loadingStatus = document.getElementById('loading-status');
    const errorStatus = document.getElementById('error-status');
    const captureArea = document.getElementById('capture-area');
    const captureDatetime = document.getElementById('capture-datetime');
    const captureTotal = document.getElementById('capture-total');

    const adminNewOrderSection = document.getElementById('admin-new-order-section'); // New
    const adminOrderForm = document.getElementById('admin-order-form');         // New
    const adminNameField = document.getElementById('admin-name');       // New
    const adminItemField = document.getElementById('admin-item');       // New
    const adminQuantityField = document.getElementById('admin-quantity'); // New
    const adminAmountField = document.getElementById('admin-amount');     // New
    const cancelNewOrderBtn = document.getElementById('cancel-new-order'); // New
    const adminOrderStatus = document.getElementById('admin-order-status'); // New


    const adminName = 'Kamran'; // Hardcoded admin name

    // Initially show the name input section and hide content
    adminNameSection.style.display = 'block';
    adminContentSection.style.display = 'none';
    adminNewOrderSection.style.display = 'none'; // Hide new order form too

    // --- Event Listener for Admin Name Submission ---
    adminNameSubmitBtn.addEventListener('click', () => {
        const enteredName = adminNameInput.value.trim();

        if (enteredName === adminName) {
            // Correct name entered
            adminNameSection.style.display = 'none';
            adminContentSection.style.display = 'block';
            fetchOrders(); // Fetch orders now that access is granted
        } else {
            // Incorrect name
            showAdminNameStatus('Incorrect name.', 'error');
        }
    });

    // Allow pressing Enter in the admin name input field
    adminNameInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
            event.preventDefault();
            adminNameSubmitBtn.click();
        }
    });

    // --- Event Listener for New Order Button ---
    newOrderBtn.addEventListener('click', () => {
        adminContentSection.style.display = 'none';
        adminNewOrderSection.style.display = 'block';
        adminOrderForm.reset(); // Clear the form
        adminOrderStatus.style.display = 'none'; // Hide status
        adminNameField.focus(); // Put focus on the first field
    });

    // --- Event Listener for Cancel New Order Button ---
    cancelNewOrderBtn.addEventListener('click', () => {
        adminNewOrderSection.style.display = 'none';
        adminContentSection.style.display = 'block';
        fetchOrders(); // Refresh list when returning
    });

    // --- Event Listener for Admin Order Form Submission ---
    adminOrderForm.addEventListener('submit', async (event) => {
        event.preventDefault();

        const name = adminNameField.value.trim();
        const item = adminItemField.value.trim();
        const quantity = adminQuantityField.value.trim();
        const amount = adminAmountField.value.trim();

         if (!name || !item || !quantity || !amount) {
            showAdminOrderStatus('Please fill in all fields.', 'error');
            return;
        }

        const orderData = {
            name: name,
            item: item,
            quantity: quantity,
            amount: amount
        };

        try {
            // Send POST request to the same /submit endpoint
            // Add admin name for backend check (simple validation)
            const response = await fetch(`/submit?name=${adminName}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData),
            });

            const result = await response.json();

            if (result.success) {
                showAdminOrderStatus('Order submitted successfully!', 'success');
                // After successful submission, hide the form and show the list
                // Use a slight delay to allow user to see the success message
                setTimeout(() => {
                    adminNewOrderSection.style.display = 'none';
                    adminContentSection.style.display = 'block';
                    fetchOrders(); // Refresh the list
                }, 1000); // 1 second delay
            } else {
                showAdminOrderStatus(`Submission failed: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Error submitting admin order:', error);
            showAdminOrderStatus('An error occurred while submitting.', 'error');
        }
    });


    // --- Fetch Orders Function ---
    async function fetchOrders() {
        loadingStatus.style.display = 'block';
        errorStatus.style.display = 'none';
        adminList.innerHTML = ''; // Clear previous list

        try {
            // Include the admin name in the request for backend validation
            const response = await fetch(`/admin/data?name=${adminName}`);
            const result = await response.json();

            loadingStatus.style.display = 'none';

            if (result.success) {
                displayOrders(result.orders);
            } else {
                errorStatus.textContent = `Error: ${result.message}`;
                errorStatus.style.display = 'block';
            }
        } catch (error) {
            console.error('Error fetching orders:', error);
            loadingStatus.style.display = 'none';
            errorStatus.textContent = 'An error occurred while fetching orders.';
            errorStatus.style.display = 'block';
        }
    }

    // --- Display Orders Function ---
    function displayOrders(orders) {
        let totalAmount = 0;
        adminList.innerHTML = ''; // Clear list

        if (orders.length === 0) {
            adminList.innerHTML = '<li>No orders submitted yet.</li>';
            captureTotal.textContent = 'Total Amount: 0';
             // Hide elements if no orders
            captureArea.querySelector('.capture-footer').style.display = 'none';
            return;
        }

        captureArea.querySelector('.capture-footer').style.display = 'block'; // Show footer if orders exist


        orders.forEach(order => {
            const listItem = document.createElement('li');
            // Add data-id and a delete button
            listItem.innerHTML = `
                <strong>${order.name}:</strong>
                <span>Item: ${order.item}</span>
                <span>Quantity: ${order.quantity}</span>
                <span>Amount: ${order.amount}</span>
                <span style="font-size: 0.7em; color: #777;">(${new Date(order.timestamp).toLocaleString()})</span>
                <button class="delete-order-btn" data-id="${order.id}">Delete</button>
            `;
            adminList.appendChild(listItem);

            // Try to parse amount as a number for total calculation
            const amountValue = parseFloat(order.amount.replace(/[^0-9.-]+/g,"")); // Remove non-numeric chars except . and -
            if (!isNaN(amountValue)) {
                totalAmount += amountValue;
            }
        });

        // Display total amount
        captureTotal.textContent = `Total Amount: ${totalAmount.toFixed(2)}`;
    }

    // --- Event Delegation for Delete Buttons ---
    adminList.addEventListener('click', (event) => {
        // Check if the clicked element is a delete button
        if (event.target.classList.contains('delete-order-btn')) {
            const orderId = event.target.dataset.id;
            if (confirm(`Are you sure you want to delete order ${orderId}?`)) {
                deleteOrder(orderId);
            }
        }
    });

    // --- Function to Delete a Single Order ---
    async function deleteOrder(orderId) {
        try {
            // Add admin name for backend check (simple validation)
            const response = await fetch(`/delete/${orderId}?name=${adminName}`, {
                method: 'DELETE',
            });

            const result = await response.json();

            if (result.success) {
                console.log(result.message);
                fetchOrders(); // Refresh the list after deletion
            } else {
                console.error(`Delete failed: ${result.message}`);
                alert(`Failed to delete order: ${result.message}`);
            }
        } catch (error) {
            console.error('Error deleting order:', error);
            alert('An error occurred while deleting the order.');
        }
    }

    // --- Event Listener for Delete All Button ---
    deleteAllBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to delete ALL orders? This cannot be undone.')) {
            deleteAllOrders();
        }
    });

     // --- Function to Delete All Orders ---
    async function deleteAllOrders() {
        try {
            // Add admin name for backend check (simple validation)
            const response = await fetch(`/delete_all?name=${adminName}`, {
                method: 'DELETE',
            });

            const result = await response.json();

            if (result.success) {
                console.log(result.message);
                fetchOrders(); // Refresh the list after deletion
            } else {
                console.error(`Delete All failed: ${result.message}`);
                alert(`Failed to delete all orders: ${result.message}`);
            }
        } catch (error) {
            console.error('Error deleting all orders:', error);
            alert('An error occurred while deleting all orders.');
        }
    }


    // --- Download Button Handler (Same as before) ---
    downloadBtn.addEventListener('click', () => {
        // Set current datetime for the image
        const now = new Date();
        captureDatetime.textContent = now.toLocaleString();

        // Use html2canvas to capture the capture-area div
        html2canvas(captureArea, {
            scale: 2, // Increase scale for better resolution
            logging: false // Disable logging
        }).then(canvas => {
            // Convert canvas to JPG data URL
            const imgData = canvas.toDataURL('image/jpeg', 0.9); // 0.9 is quality

            // Create a temporary link element
            const link = document.createElement('a');
            const timestamp = now.toISOString().replace(/[:.-]/g, ''); // Format timestamp for filename
            link.download = `office_orders_${timestamp}.jpg`; // Filename
            link.href = imgData;

            // Append to body and trigger click
            document.body.appendChild(link);
            link.click();

            // Clean up
            document.body.removeChild(link);

            // Clear datetime after capture (optional, keeps the page clean)
            captureDatetime.textContent = '';
        });
    });

    // --- Helper function for admin name input status ---
    function showAdminNameStatus(message, type) {
        adminNameStatus.textContent = message;
        adminNameStatus.className = `status ${type}`; // 'status success' or 'status error'
        adminNameStatus.style.display = 'block';
    }

    // --- Helper function for admin order status ---
     function showAdminOrderStatus(message, type) {
        adminOrderStatus.textContent = message;
        adminOrderStatus.className = `status ${type}`; // 'status success' or 'status error'
        adminOrderStatus.style.display = 'block';
    }

    // No initial fetchOrders call here, it happens after successful name entry
});
