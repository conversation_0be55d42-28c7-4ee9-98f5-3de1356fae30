document.addEventListener('DOMContentLoaded', () => {
    const nameInputSection = document.getElementById('name-input-section');
    const orderFormSection = document.getElementById('order-form-section');
    const userNameInput = document.getElementById('user-name-input');
    const saveNameBtn = document.getElementById('save-name-btn');
    const displayName = document.getElementById('display-name');
    const orderForm = document.getElementById('order-form');
    const statusMessage = document.getElementById('status-message');

    const userNameKey = 'peonAppName'; // Key for local storage

    // --- Check for saved name ---
    const savedName = localStorage.getItem(userNameKey);

    if (savedName) {
        showOrderForm(savedName);
    } else {
        showNameInput();
    }

    // --- Event Listeners ---
    saveNameBtn.addEventListener('click', () => {
        const name = userNameInput.value.trim();
        if (name) {
            localStorage.setItem(userNameKey, name);
            showOrderForm(name);
        } else {
            alert('Please enter your name.');
        }
    });

    orderForm.addEventListener('submit', async (event) => {
        event.preventDefault(); // Prevent default form submission

        const name = localStorage.getItem(userNameKey); // Get name from local storage
        const item = document.getElementById('item').value.trim();
        const quantity = document.getElementById('quantity').value.trim();
        const amount = document.getElementById('amount').value.trim();

        if (!name || !item || !quantity || !amount) {
            showStatus('Please fill in all fields.', 'error');
            return;
        }

        const orderData = {
            name: name,
            item: item,
            quantity: quantity,
            amount: amount
        };

        try {
            const response = await fetch('/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData),
            });

            const result = await response.json();

            if (result.success) {
                showStatus('Order submitted successfully!', 'success');
                orderForm.reset(); // Clear the form
            } else {
                showStatus(`Submission failed: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Error submitting order:', error);
            showStatus('An error occurred while submitting.', 'error');
        }
    });

    // --- Helper Functions ---
    function showNameInput() {
        nameInputSection.style.display = 'block';
        orderFormSection.style.display = 'none';
        statusMessage.style.display = 'none';
    }

    function showOrderForm(name) {
        displayName.textContent = name;
        nameInputSection.style.display = 'none';
        orderFormSection.style.display = 'block';
        statusMessage.style.display = 'none'; // Hide status message initially
    }

    function showStatus(message, type) {
        statusMessage.textContent = message;
        statusMessage.className = `status ${type}`; // 'status success' or 'status error'
        statusMessage.style.display = 'block';
    }
});
