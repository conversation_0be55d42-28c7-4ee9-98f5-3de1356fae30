<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Peon Order App</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div id="name-input-section">
            <h2>Welcome! What's your name?</h2>
            <input type="text" id="user-name-input" placeholder="Enter your name">
            <button id="save-name-btn">Save Name</button>
        </div>

        <div id="order-form-section" style="display: none;">
            <h2 id="form-title">What do you want?</h2>
            <p>Hello, <strong id="display-name"></strong>!</p>
            <form id="order-form">
                <div class="form-group">
                    <label for="item">Item:</label>
                    <input type="text" id="item" required>
                    <small class="description">e.g., Coffee, Snacks, Printout (specify file name)</small>
                </div>
                <div class="form-group">
                    <label for="quantity">Quantity:</label>
                    <input type="text" id="quantity" required>
                    <small class="description">e.g., 1 cup, 2 packets, 10 pages</small>
                </div>
                <div class="form-group">
                    <label for="amount">Amount (if applicable):</label>
                    <input type="text" id="amount" required>
                    <small class="description">e.g., 50 Rs, 100 Rs, 0 Rs (if free)</small>
                </div>
                <button type="submit">Submit Order</button>
            </form>
            <div id="status-message" class="status"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
