body {
    font-family: sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: flex-start; /* Align items to the top */
    min-height: 100vh;
}

.container {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px; /* Limit width for better readability */
}

h2 {
    text-align: center;
    color: #555;
    margin-bottom: 20px;
}

#name-input-section,
#order-form-section,
#admin-name-section, /* Added admin name section */
#admin-content-section, /* Added admin content section */
#admin-new-order-section /* Added admin new order section */
{
    margin-bottom: 20px;
}

#user-name-input,
#order-form input[type="text"],
#admin-name-input, /* Added admin name input */
#admin-order-form input[type="text"] /* Added admin order form inputs */
{
    width: calc(100% - 22px); /* Adjust for padding and border */
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
}

button {
    display: inline-block;
    background-color: #5cb85c;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
    margin-right: 5px; /* Add some spacing between buttons */
}

button:last-child {
    margin-right: 0;
}


button:hover {
    background-color: #4cae4c;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group small.description {
    display: block;
    color: #777;
    font-size: 0.8em;
    margin-top: 3px;
}

.status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
}

.status.success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.status.error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

/* Admin Page Specific Styles */
.admin-actions { /* Style for the buttons div */
    text-align: center;
    margin-bottom: 20px;
}

#admin-list {
    list-style: none;
    padding: 0;
    margin-top: 20px;
}

#admin-list li {
    background-color: #eee;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    border-left: 5px solid #5cb85c;
    position: relative; /* Needed for positioning the delete button */
    padding-right: 80px; /* Make space for the delete button */
}

#admin-list li strong {
    color: #333;
}

#admin-list li span {
    display: block;
    font-size: 0.9em;
    color: #555;
    margin-top: 3px;
}

.delete-order-btn {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    background-color: #d9534f; /* Red color for delete */
    color: white;
    padding: 3px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8em;
    transition: background-color 0.3s ease;
}

.delete-order-btn:hover {
    background-color: #c9302c;
}


#download-btn {
    margin-top: 20px;
    background-color: #0275d8;
}

#download-btn:hover {
    background-color: #025aa5;
}

#delete-all-btn { /* Specific style for delete all */
    background-color: #d9534f;
}
#delete-all-btn:hover {
    background-color: #c9302c;
}

#total-amount { /* This is for the old total display, replaced by capture-total */
    display: none; /* Hide the old total */
}

/* Styles for the element to be captured by html2canvas */
#capture-area {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
    box-sizing: border-box; /* Include padding in width */
}

#capture-area h2 {
     margin-bottom: 10px;
}

#capture-area p {
    margin-bottom: 5px;
}

#capture-area ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#capture-area li {
    background-color: #f9f9f9;
    padding: 8px;
    margin-bottom: 8px;
    border-left: 4px solid #5cb85c;
    font-size: 0.9em;
    /* Remove delete button from capture area */
    position: static;
    padding-right: 8px;
}

#capture-area li strong {
    display: inline-block;
    min-width: 80px; /* Align labels */
}

#capture-area .capture-header,
#capture-area .capture-footer {
    text-align: center;
    margin-bottom: 15px;
    font-size: 0.9em;
    color: #666;
}

#capture-area .capture-footer {
    margin-top: 15px;
    border-top: 1px dashed #ccc;
    padding-top: 10px;
    font-weight: bold;
    font-size: 1em;
    color: #333;
}

#capture-area #capture-total {
    text-align: right;
    margin-top: 10px;
    font-weight: bold;
}

#admin-new-order-section h3 { /* Style for the new form title */
    text-align: center;
    color: #555;
    margin-bottom: 20px;
}

#admin-order-form button[type="button"] { /* Style for the cancel button */
    background-color: #f0ad4e;
}
#admin-order-form button[type="button"]:hover {
    background-color: #ec971f;
}

